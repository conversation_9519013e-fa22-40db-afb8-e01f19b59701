import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../features/screen/business/model/addproductsmodel.dart';
import '../../utlis/app_config/app_config.dart';



class ProductApiService {
  // Fetch products for the business
  static Future<List<ProductRequest>> fetchMyProducts(String token) async {
    final url = Uri.parse('${AppConfig.baseUrl}/product/business/list');

    final response = await http.get(
      url,
      headers: {
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      final List<dynamic> dataList = decoded['data'] ?? [];
      return dataList.map<ProductRequest>((item) => ProductRequest.fromJson(item)).toList();
    } else {
      print("❌ Failed to fetch products: ${response.statusCode}");
      return [];
    }
  }

  // Create a new product
  static Future<bool> createProduct({
    required String token,
    required ProductRequest product,
  }) async {
    final url = Uri.parse('${AppConfig.baseUrl}/product/');

    final response = await http.post(
      url,
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      },
      body: jsonEncode(product.toJson()),
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      print('✅ Product created: ${response.body}');
      return true;
    } else {
      print('❌ Error: ${response.statusCode}');
      print(response.body);
      return false;
    }
  }
}
